<?php

namespace AwardForce\Modules\Forms\Forms\Bus;

use Carbon\Carbon;

abstract class UpdateSubmittable
{
    public function __construct(
        private ?int $submittableId,
        private ?int $userId,
        private ?array $values,
        private ?Carbon $time
    ) {
    }

    public function submittableId(): ?int
    {
        return $this->submittableId;
    }

    public function userId(): ?int
    {
        return $this->userId;
    }

    public function values(): ?array
    {
        return $this->values;
    }

    public function time(): ?int
    {
        return $this->time->timestamp;
    }
}
