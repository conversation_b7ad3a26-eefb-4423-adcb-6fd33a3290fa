<?php

namespace AwardForce\Modules\Forms\Forms\Bus;

use AwardForce\Modules\Api\V2\Services\ApiFieldFormat;
use AwardForce\Modules\Forms\Fields\Database\Repositories\FieldRepository;
use AwardForce\Modules\Forms\Fields\Services\ValuesService;
use Platform\Events\EventDispatcher;

class UpdateFieldValueHandler
{
    use ApiFieldFormat,
        EventDispatcher;

    public function __construct(private ValuesService $values, private FieldRepository $fields)
    {
    }

    public function handle(UpdateFieldValue $command)
    {
        $field = $this->fields->requireBySlug($command->fieldSlug);

        $hasValues = $this->values->syncIndividualFieldValueForObject(
            $command->hasValues,
            $field,
            $this->format($field, $command->value),
        );

        $this->dispatchAll($hasValues->releaseEvents());
    }
}
