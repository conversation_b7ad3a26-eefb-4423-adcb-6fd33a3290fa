<?php

namespace AwardForce\Modules\Entries\Commands;

use AwardForce\Modules\Forms\Forms\Bus\UpdateSubmittable;
use Carbon\Carbon;

class UpdateEntry extends UpdateSubmittable
{
    public function __construct(
        protected ?int $submitabbleId,
        protected ?int $userId,
        protected ?array $values,
        protected ?Carbon $time,
        protected ?string $title,
        protected ?int $chapterId,
        protected ?int $categoryId,
        protected ?bool $isEntrant,
        protected ?string $status = null,
        protected ?string $moderationStatus = null,
        protected ?string $grantStatus = null,
        protected ?string $grantEndDate = null,
        protected ?string $customDeadline = null
    ) {
        parent::__construct($this->submitabbleId, $this->userId, $this->values, $this->time);
    }

    public function title(): ?string
    {
        return $this->title;
    }

    public function chapterId(): ?int
    {
        return $this->chapterId;
    }

    public function categoryId(): ?int
    {
        return $this->categoryId;
    }

    public function isEntrant(): ?bool
    {
        return $this->isEntrant;
    }

    public function status(): ?string
    {
        return $this->status;
    }

    public function moderationStatus(): ?string
    {
        return $this->moderationStatus;
    }

    public function grantStatus(): ?string
    {
        return $this->grantStatus;
    }

    public function grantEndDate(): ?string
    {
        return $this->grantEndDate;
    }

    public function customDeadline(): ?string
    {
        return $this->customDeadline;
    }
}
