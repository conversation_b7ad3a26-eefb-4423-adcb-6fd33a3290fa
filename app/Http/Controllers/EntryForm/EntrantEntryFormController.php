<?php

namespace AwardForce\Http\Controllers\EntryForm;

use AwardForce\Http\Requests\Entry\Entrant\EditEntryRequest;
use AwardForce\Http\Requests\Entry\Entrant\SingleElementUpdateRequest;
use AwardForce\Http\Requests\Entry\Entrant\StartEntryRequest;
use AwardForce\Http\Requests\Entry\Entrant\SubmitEntryRequest;
use AwardForce\Http\Requests\Entry\Entrant\UpdateEntryRequest;
use AwardForce\Library\Authorization\Consumer;
use AwardForce\Modules\Accounts\Facades\CurrentAccount;
use AwardForce\Modules\Content\Blocks\Contracts\ContentBlockRepository;
use AwardForce\Modules\Entries\Commands\UpdateEntryCategory;
use AwardForce\Modules\Entries\Commands\UpdateEntryChapter;
use AwardForce\Modules\Entries\Commands\UpdateEntryTitle;
use AwardForce\Modules\Entries\Contracts\AttachmentRepository;
use AwardForce\Modules\Entries\Contracts\ContributorRepository;
use AwardForce\Modules\Entries\Services\EntrantEntryFormService;
use AwardForce\Modules\Entries\Services\EntryCategoriesListGenerator;
use AwardForce\Modules\Entries\View\Entrant\EntrantEntryForm;
use AwardForce\Modules\Forms\Forms\Bus\CreateContributor;
use AwardForce\Modules\Forms\Forms\Bus\CreateLink;
use AwardForce\Modules\Forms\Forms\Bus\DeleteLink;
use AwardForce\Modules\Forms\Forms\Bus\RemoveContributor;
use AwardForce\Modules\Forms\Forms\Bus\UpdateFieldValue;
use AwardForce\Modules\Forms\Forms\Bus\UpdateLink;
use AwardForce\Modules\Forms\Forms\Database\Entities\Submittable;
use AwardForce\Modules\Forms\Forms\Database\Repositories\FormRepository;
use AwardForce\Modules\Referees\Commands\CreateReferee;
use AwardForce\Modules\Referees\Commands\RemoveReferee;
use AwardForce\Modules\Referees\Commands\UpdateReferee;
use AwardForce\Modules\Referees\Contracts\RefereeRepository;
use AwardForce\Modules\Rounds\Services\RoundStatus;
use AwardForce\Modules\Seasons\Facades\SeasonFilter;
use AwardForce\Modules\Seasons\Models\Season;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

class EntrantEntryFormController extends EntryFormController
{
    public static $resource = 'EntriesOwner';

    public function __construct(
        protected EntrantEntryFormService $submittableFormService,
        protected RoundStatus $rounds,
        protected ContentBlockRepository $contentBlocks,
        protected EntryCategoriesListGenerator $generator,
        protected FormRepository $forms
    ) {
    }

    protected function isEntrant(): bool
    {
        return true;
    }

    /**
     * @return mixed
     */
    public function getStart(EntrantEntryForm $view)
    {
        return $this->renderStartView($view, 'entry-form.entrant');
    }

    /**
     * @return mixed
     */
    public function getEdit(EditEntryRequest $request, EntrantEntryForm $view)
    {
        return $this->renderEditView($view, 'entry-form.entrant');
    }

    protected function getSeason(): Season
    {
        if (Consumer::can('update', 'EntriesAll')) {
            return SeasonFilter::get() ?: CurrentAccount::activeSeason();
        }

        return CurrentAccount::activeSeason();
    }

    /**
     * Save the entry into the database.
     *
     * @return mixed
     */
    public function startEntry(StartEntryRequest $request)
    {
        $entry = $this->newEntry($request);

        if (! setting()->entryPaymentOnStart()) {
            return response()->json([
                'entry' => $this->mapEntry($entry),
            ]);
        }

        $this->addToCart($entry, false);

        return response()->json(['redirect' => 'cart']);
    }

    public function autosave(UpdateEntryRequest $request)
    {
        return $this->updateSubmittable($request);
    }

    public function pay(UpdateEntryRequest $request)
    {
        $this->updateEntry($request);
        $this->addToCart($request->entry, false);

        return response()->json(['redirect' => 'cart']);
    }

    public function startAndSubmit(StartEntryRequest $request)
    {
        $entry = $this->newEntry($request);

        try {
            $request->route()->setParameter('entry', $entry);
            $submitEntryRequest = app(SubmitEntryRequest::class, $request->all());

            return $this->submit($submitEntryRequest);
        } catch (ValidationException $e) {
            $messages = $e->errors();
            $messages['error-response-extra-data-entry'] = [$this->mapEntry($entry)];
            throw ValidationException::withMessages($messages);
        }
    }

    public function updateAndSubmit(SubmitEntryRequest $request)
    {
        $this->updateEntry($request);

        return $this->submit($request);
    }

    protected function getUserId(Request $request)
    {
        return $request->entry?->userId ?: Consumer::get()->id();
    }

    protected function paidEntries(): bool
    {
        return setting()->paidEntries();
    }

    protected function redirectAfterSubmit(?Submittable $submittable = null)
    {
        if ($this->contentBlocks->getByKeysForRoles('submission-completed', Consumer::get()->user()->roles->pluck('id'))->count() > 0) {
            return response()->json([
                'redirect' => 'complete',
                'submittableSlug' => (string) $submittable->slug,
                'submittable' => 'entry-form',
            ]);
        }

        return response()->json(['redirect' => 'index']);
    }

    public function updateAndSubmitEligibility(SubmitEntryRequest $request)
    {
        return $this->processEligibilityRequest($request);
    }

    public function updateField(SingleElementUpdateRequest $request)
    {
        $this->dispatch(UpdateFieldValue::forSubmittable(
            $this->forms,
            (string) $request->entry->form->slug,
            (string) $request->entry->slug,
            (string) $request->field->slug,
            $value = $request->get('value')
        ));
        $entry = $request->entry->refresh();

        return response()->json(['success' => true, 'value' => $value, 'updatedAt' => $entry->updatedAt->format('Y-m-d H:i:s')]);
    }

    public function updateAttachment(SingleElementUpdateRequest $request, AttachmentRepository $attachments)
    {
        $this->dispatch(UpdateFieldValue::forAttachment(
            $attachments,
            $request->attachment,
            (string) $request->field->slug,
            $value = $request->get('value')
        ));

        return response()->json(['success' => true, 'value' => $value]);
    }

    public function createContributor(SingleElementUpdateRequest $request)
    {
        $contributor = $this->dispatchSync(
            new CreateContributor($request->entry, $request->tab)
        );

        return response()->json(['success' => true, 'value' => $contributor->id]);
    }

    public function updateContributor(SingleElementUpdateRequest $request, ContributorRepository $contributors)
    {
        $this->dispatch(UpdateFieldValue::forContributor(
            $contributors,
            $request->contributor,
            (string) $request->field->slug,
            $value = $request->get('value')
        ));

        return response()->json(['success' => true, 'value' => $value]);
    }

    public function removeContributor(SingleElementUpdateRequest $request): JsonResponse
    {
        $this->dispatch(new RemoveContributor($request->contributor));

        return response()->json(['success' => true]);
    }

    public function createReferee(SingleElementUpdateRequest $request): JsonResponse
    {
        $referee = $this->dispatchSync(new CreateReferee($request->entry, $request->tab));

        return response()->json(['success' => true, 'value' => $referee->id]);
    }

    public function updateReferee(SingleElementUpdateRequest $request): JsonResponse
    {
        $this->dispatch(new UpdateReferee(
            $request->route('referee'),
            $request->string('name'),
            $request->string('email')
        ));

        return response()->json(['success' => true]);
    }

    public function updateRefereeField(SingleElementUpdateRequest $request, RefereeRepository $referees): JsonResponse
    {
        $this->dispatch(UpdateFieldValue::forReferee(
            $referees,
            $request->referee,
            (string) $request->field->slug,
            $value = $request->get('value')
        ));

        return response()->json(['success' => true, 'value' => $value]);
    }

    public function removeReferee(SingleElementUpdateRequest $request): JsonResponse
    {
        $this->dispatch(new RemoveReferee($request->referee));

        return response()->json(['success' => true]);
    }

    public function createLink(SingleElementUpdateRequest $request)
    {
        $link = $this->dispatchSync(new CreateLink(
            $request->entry,
            $request->tab->id
        ));

        return response()->json(['success' => true, 'value' => $link->id]);
    }

    public function updateLink(SingleElementUpdateRequest $request)
    {
        $this->dispatch(new UpdateLink(
            $request->entry,
            $request->link,
            $url = $request->get('url'),
            $extra = $request->get('extra'))
        );

        return response()->json(['success' => true, 'value' => ['url' => $url, 'extra' => $extra]]);
    }

    public function deleteLink(SingleElementUpdateRequest $request)
    {
        $this->dispatch(new DeleteLink($request->entry, $request->link));

        return response()->json(['success' => true]);
    }

    public function updateTitle(SingleElementUpdateRequest $request)
    {
        $this->dispatch(new UpdateEntryTitle($request->entry, $title = $request->get('title')));

        return response()->json(['success' => true, 'value' => $title]);
    }

    public function updateCategory(SingleElementUpdateRequest $request)
    {
        $this->dispatch(new UpdateEntryCategory($request->entry, $categoryId = $request->get('categoryId')));

        return response()->json(['success' => true, 'value' => $categoryId]);
    }

    public function updateChapter(SingleElementUpdateRequest $request)
    {
        $this->dispatch(new UpdateEntryChapter($request->entry, $chapterId = $request->get('chapterId')));

        return response()->json(['success' => true, 'value' => $chapterId]);
    }
}
