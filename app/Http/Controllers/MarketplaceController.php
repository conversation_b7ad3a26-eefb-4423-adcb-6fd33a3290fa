<?php

namespace AwardForce\Http\Controllers;

use AwardForce\Http\Requests\Integration\MarketplaceEventRequest;
use AwardForce\Modules\Integrations\Marketplace\Events\PrismaticEvent;
use AwardForce\Modules\Integrations\Marketplace\Services\EventManager;
use AwardForce\Modules\Integrations\Marketplace\Views\MarketplaceView;
use Platform\Events\EventDispatcher;
use Platform\Http\Controller;

class MarketplaceController extends Controller
{
    use EventDispatcher;

    public static $resource = ['Integrations'];

    public function __construct(private EventManager $eventManager)
    {
        $this->middleware('auth.role');
    }

    public function index(MarketplaceView $view)
    {
        return $this->respond('marketplace.landing', $view);
    }

    public function event(MarketplaceEventRequest $request)
    {
        $this->eventManager->handle(PrismaticEvent::create($request->eventName(), $request->payloadData()));

        if ($request->expectsJson()) {
            return response()->json(['success' => true]);
        }
    }
}
